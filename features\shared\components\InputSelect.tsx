import { Ionicons } from "@expo/vector-icons";
import React, { useState, useRef } from "react";
import {
  StyleProp,
  Text,
  TextStyle,
  View,
  ViewStyle,
  StyleSheet,
  Pressable,
} from "react-native";
import globalStyles from "@/lib/globalStyles";
import { screenWidth } from "@/lib/device";
import Dropdown, { DropdownItem } from "./Dropdown";
import { useTranslation } from "react-i18next";

type ItemType<Type> = Type extends {
  name?: string;
  value?: string;
}
  ? Type
  : never;

type Props<Type> = {
  items: ItemType<Type>[];
  selected?: ItemType<Type>;
  onSelectItem?: (item: ItemType<Type>) => void;
  label?: string;
  style?: StyleProp<ViewStyle>;
  textStyle?: StyleProp<TextStyle>;
  dropdownStyle?: StyleProp<ViewStyle>;
  listStyle?: StyleProp<ViewStyle>;
  theme?: "primary" | "secondary";
  size?: "sm" | "md";
  withSearch?: boolean;
  defaultValue?: string;
  required?: boolean;
  alignment?: "left" | "right" | "center";
};

const InputSelect = <Type,>({
  items,
  selected,
  label,
  onSelectItem,
  style,
  textStyle,
  theme = "primary",
  size = "sm",
  withSearch = false,
  defaultValue,
  dropdownStyle,
  listStyle,
  required = false,
  alignment = "left",
}: Props<Type>) => {
  const { t } = useTranslation();
  const [isDropdownVisible, setIsDropdownVisible] = useState(false);
  const triggerRef = useRef<View>(null);

  const renderLabel = () => {
    if (selected || isDropdownVisible) {
      return <Text style={[styles.label]}>{label}</Text>;
    }
    return null;
  };

  const getThemeStyles = () => {
    switch (theme) {
      case "secondary":
        return styles.secondaryTheme;
      default:
        return styles.primaryTheme;
    }
  };

  // Convert items to DropdownItem format
  const dropdownItems: DropdownItem<string | undefined>[] = items.map(
    (item, index) => ({
      id: item.value || index.toString(),
      label: item.name || "",
      value: item.value,
    })
  );

  const handleSelectItem = (item: DropdownItem<string | undefined>) => {
    // Find the original item to maintain type compatibility
    const originalItem = items.find((i) => i.value === item.value);
    if (originalItem) {
      onSelectItem?.(originalItem);
    }
    setIsDropdownVisible(false);
  };

  const getDisplayText = () => {
    if (selected?.name) return selected.name;
    if (defaultValue) {
      const defaultItem = items.find((item) => item.value === defaultValue);
      return defaultItem?.name || defaultValue;
    }
    return label;
  };

  const getTextColor = () => {
    if (selected?.value || defaultValue) {
      return globalStyles.rgba().primary1;
    }
    return globalStyles.rgba().tertiary2;
  };

  return (
    <>
      <Pressable
        ref={triggerRef}
        style={[styles.container, getThemeStyles(), style]}
        onPress={() => setIsDropdownVisible(true)}
        android_ripple={{
          color: globalStyles.rgba().light.primary,
          borderless: false,
        }}
      >
        {required && (
          <View className="absolute top-0 left-0 size-2 rounded-full bg-primary-2 z-10" />
        )}
        {renderLabel()}
        <View style={[styles.dropdown, dropdownStyle]}>
          <Text
            style={[styles.textStyle, textStyle, { color: getTextColor() }]}
            numberOfLines={1}
            ellipsizeMode="tail"
          >
            {getDisplayText()}
          </Text>
          <Ionicons
            color={globalStyles.rgba().primary1}
            name="caret-down-sharp"
            size={15}
          />
        </View>
      </Pressable>

      <Dropdown
        items={dropdownItems}
        onSelectItem={handleSelectItem}
        visible={isDropdownVisible}
        onClose={() => setIsDropdownVisible(false)}
        triggerRef={triggerRef}
        alignment={alignment}
        searchable={withSearch}
        searchPlaceholder={t("common.search")}
        containerStyle={listStyle}
        itemTextStyle={{
          color: globalStyles.colors.dark.secondary,
        }}
        overlayClassName="bg-black/0"
      />
    </>
  );
};

export default InputSelect;

const styles = StyleSheet.create({
  container: {
    position: "relative",
    justifyContent: "center",
    padding: 0,
    paddingHorizontal: globalStyles.gap.xs,
    paddingVertical: globalStyles.gap["2xs"],
    borderWidth: 1,
    borderColor: globalStyles.colors.primary1,
    borderRadius: globalStyles.rounded.full,
    minHeight: 44,
  },
  dropdown: {
    width: "100%",
    minWidth: 100,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 0,
    gap: globalStyles.gap["2xs"],
  },
  label: {
    position: "absolute",
    top: -18,
    fontSize: globalStyles.size.sm,
    color: globalStyles.colors.tertiary2,
    paddingLeft: globalStyles.size.xs,
  },
  textStyle: {
    fontSize: globalStyles.size.lg,
    textAlign: "center",
    lineHeight: globalStyles.size.textAdjustLineHeight,
    maxWidth: "80%",
  },
  primaryTheme: {
    backgroundColor: "transparent",
  },
  secondaryTheme: {
    backgroundColor: "white",
  },
});
