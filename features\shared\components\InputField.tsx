import globalStyles from "@/lib/globalStyles";
import { forwardRef } from "react";
import {
  View,
  StyleProp,
  ViewStyle,
  TextInput,
  StyleSheet,
  TextInputProps,
} from "react-native";

type Props = TextInputProps & {
  required?: boolean;
  theme?: "primary" | "secondary";
  containerStyle?: StyleProp<ViewStyle>;
};

const InputField = forwardRef<TextInput, Props>(
  ({ style, theme, containerStyle, ...props }, ref) => {
    const isSecondary = theme === "secondary";
    return (
      <View style={[styles.container, containerStyle]}>
        {props.required && (
          <View className="absolute top-0 left-0 size-2 rounded-full bg-primary-2 z-10" />
        )}
        <TextInput
          ref={ref}
          {...props}
          style={[
            styles.input,
            {
              backgroundColor: isSecondary
                ? globalStyles.colors.white
                : globalStyles.colors.light.primary,
            },
            style,
          ]}
        />
      </View>
    );
  }
);

const styles = StyleSheet.create({
  container: {
    position: "relative",
    width: "100%",
    flexShrink: 1,
  },
  input: {
    backgroundColor: globalStyles.colors.light.primary,
    borderRadius: globalStyles.rounded.xs,
    fontSize: globalStyles.size.lg,
    paddingHorizontal: globalStyles.gap.xs,
    paddingVertical: globalStyles.gap["2xs"],
    width: "100%",
    flexShrink: 1,
    color: globalStyles.colors.primary1,
    minHeight: 44,
  },
});

export default InputField;
